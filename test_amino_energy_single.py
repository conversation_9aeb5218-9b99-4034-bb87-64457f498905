#!/usr/bin/env python3
"""
Test script for Amino Energy Grape single-object detection
Focus on fixing the massive bounding box issue
"""

import logging
import sys
from pathlib import Path
import torch
from ultralytics import YOLO
import cv2
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_amino_energy_detection():
    """Test single-object detection on Amino Energy Grape video only"""
    
    # Paths
    model_path = Path("images_quadcam_results/trained_models/Amino_Energy_Grape_model/weights/best.pt")
    video_path = Path("QuadCamTestVideos/AminoEnergyGrape/cam0.mp4")
    output_path = Path("test_amino_single_object.mp4")
    
    # Check if model exists
    if not model_path.exists():
        logger.error(f"❌ Model not found: {model_path}")
        return False
    
    # Check if video exists
    if not video_path.exists():
        logger.error(f"❌ Video not found: {video_path}")
        return False
    
    # Load model
    logger.info(f"🤖 Loading model: {model_path}")
    model = YOLO(str(model_path))
    
    # Open video
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        logger.error(f"❌ Could not open video: {video_path}")
        return False
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    logger.info(f"📹 Video: {width}x{height}, {fps} FPS, {total_frames} frames")
    
    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
    
    # Robust tracking parameters
    frame_count = 0
    tracking_frames = 0
    confidence_scores = []
    detection_history = []
    stable_detection = None
    no_detection_frames = 0
    
    # Realistic parameters based on diagnostic analysis
    HIGH_CONF_THRESHOLD = 0.65  # Reasonable confidence threshold (model max is ~0.78)
    MIN_DETECTION_FRAMES = 5    # Fewer frames needed for faster response
    MAX_NO_DETECTION = 20       # Reset if no detection
    MIN_BOX_SIZE = 5000         # Minimum size for actual product
    MAX_BOX_SIZE = width * height * 0.15  # Much smaller maximum (15% of frame) to avoid massive boxes
    
    logger.info(f"🎯 Processing with strict parameters:")
    logger.info(f"   - Confidence threshold: {HIGH_CONF_THRESHOLD}")
    logger.info(f"   - Min detection frames: {MIN_DETECTION_FRAMES}")
    logger.info(f"   - Box size range: {MIN_BOX_SIZE} - {MAX_BOX_SIZE:.0f}")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Run YOLO with very high confidence
        results = model(frame, conf=HIGH_CONF_THRESHOLD, verbose=False)
        
        current_detections = []
        
        # Process detections with strict filtering
        if results[0].boxes is not None and len(results[0].boxes) > 0:
            boxes = results[0].boxes
            
            for box in boxes:
                conf = float(box.conf[0])
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                box_area = (x2 - x1) * (y2 - y1)
                
                # Very strict size filtering
                if MIN_BOX_SIZE <= box_area <= MAX_BOX_SIZE:
                    # Calculate combined score favoring smaller boxes
                    size_score = 1.0 / (box_area / 10000)  # Smaller boxes get higher scores
                    combined_score = conf * 0.7 + size_score * 0.3  # Weight confidence more

                    current_detections.append({
                        'box': [x1, y1, x2, y2],
                        'conf': conf,
                        'area': box_area,
                        'center_x': (x1 + x2) / 2,
                        'center_y': (y1 + y2) / 2,
                        'score': combined_score
                    })
        
        # Take only the best detection (prioritizing smaller boxes)
        if current_detections:
            current_detections.sort(key=lambda x: x['score'], reverse=True)  # Sort by combined score
            best_detection = current_detections[0]
            
            detection_history.append(best_detection)
            no_detection_frames = 0
            
            # Keep reasonable history
            if len(detection_history) > MIN_DETECTION_FRAMES * 2:
                detection_history = detection_history[-MIN_DETECTION_FRAMES * 2:]
            
            # Check for stable detection
            if len(detection_history) >= MIN_DETECTION_FRAMES:
                recent_detections = detection_history[-MIN_DETECTION_FRAMES:]
                avg_conf = np.mean([d['conf'] for d in recent_detections])
                
                # Position consistency check
                centers_x = [d['center_x'] for d in recent_detections]
                centers_y = [d['center_y'] for d in recent_detections]
                position_variance = np.var(centers_x) + np.var(centers_y)
                
                # Very strict criteria for stable detection
                if avg_conf >= HIGH_CONF_THRESHOLD and position_variance < 5000:
                    stable_detection = best_detection
        else:
            no_detection_frames += 1
            if no_detection_frames > MAX_NO_DETECTION:
                stable_detection = None
                detection_history = []
        
        # Draw only stable detection
        if stable_detection is not None:
            tracking_frames += 1
            confidence_scores.append(stable_detection['conf'])
            
            box = stable_detection['box']
            conf = stable_detection['conf']
            x1, y1, x2, y2 = [int(coord) for coord in box]
            
            # Draw very visible bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 5)
            
            # Draw corner markers
            corner_size = 25
            cv2.line(frame, (x1, y1), (x1 + corner_size, y1), (0, 255, 0), 8)
            cv2.line(frame, (x1, y1), (x1, y1 + corner_size), (0, 255, 0), 8)
            cv2.line(frame, (x2, y1), (x2 - corner_size, y1), (0, 255, 0), 8)
            cv2.line(frame, (x2, y1), (x2, y1 + corner_size), (0, 255, 0), 8)
            cv2.line(frame, (x1, y2), (x1 + corner_size, y2), (0, 255, 0), 8)
            cv2.line(frame, (x1, y2), (x1, y2 - corner_size), (0, 255, 0), 8)
            cv2.line(frame, (x2, y2), (x2 - corner_size, y2), (0, 255, 0), 8)
            cv2.line(frame, (x2, y2), (x2, y2 - corner_size), (0, 255, 0), 8)
            
            # Large, clear label
            label = f"Amino Energy Grape: {conf:.1%}"
            font_scale = 1.2
            thickness = 3
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
            
            # Background for text
            cv2.rectangle(frame, (x1, y1 - label_size[1] - 25), 
                         (x1 + label_size[0] + 25, y1), (0, 255, 0), -1)
            cv2.putText(frame, label, (x1 + 12, y1 - 12), 
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), thickness)
            
            # Status
            status = f"TRACKING - Frame: {frame_count}/{total_frames}"
            cv2.putText(frame, status, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 3)
        else:
            # No detection
            status = f"SEARCHING - Frame: {frame_count}/{total_frames}"
            cv2.putText(frame, status, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 3)
        
        # Write frame
        out.write(frame)
        
        # Progress
        if frame_count % 100 == 0:
            progress = (frame_count / total_frames) * 100
            logger.info(f"📹 Progress: {progress:.1f}% - Tracking: {tracking_frames} frames")
    
    # Cleanup
    cap.release()
    out.release()
    
    # Results
    tracking_rate = tracking_frames / total_frames if total_frames > 0 else 0
    avg_confidence = np.mean(confidence_scores) if confidence_scores else 0
    
    logger.info(f"✅ Test completed!")
    logger.info(f"📊 Results:")
    logger.info(f"   - Total frames: {total_frames}")
    logger.info(f"   - Tracking frames: {tracking_frames}")
    logger.info(f"   - Tracking rate: {tracking_rate:.1%}")
    logger.info(f"   - Average confidence: {avg_confidence:.1%}")
    logger.info(f"   - Output saved: {output_path}")
    
    return True

if __name__ == "__main__":
    logger.info("🚀 Testing Amino Energy Grape Single-Object Detection")
    logger.info("=" * 60)
    
    success = test_amino_energy_detection()
    
    if success:
        logger.info("✅ Test completed successfully!")
    else:
        logger.error("❌ Test failed!")
        sys.exit(1)
