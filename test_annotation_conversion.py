#!/usr/bin/env python3
"""
Test LabelMe to YOLO annotation conversion
"""

import json
import cv2
from pathlib import Path

def convert_labelme_to_yolo(json_file: Path, img_width: int, img_height: int):
    """Convert LabelMe annotation to YOLO format"""
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        yolo_lines = []
        
        for shape in data.get('shapes', []):
            if shape['shape_type'] == 'rectangle':
                points = shape['points']
                x1, y1 = points[0]
                x2, y2 = points[1]
                
                # Convert to YOLO format (normalized center x, center y, width, height)
                center_x = (x1 + x2) / 2 / img_width
                center_y = (y1 + y2) / 2 / img_height
                width = abs(x2 - x1) / img_width
                height = abs(y2 - y1) / img_height
                
                # Class ID (we'll use 0 for all products since we train one model per product)
                class_id = 0
                
                yolo_lines.append(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}")
            
            elif shape['shape_type'] == 'polygon':
                # Convert polygon to bounding box
                points = shape['points']
                if len(points) >= 3:  # Valid polygon needs at least 3 points
                    # Extract x and y coordinates
                    x_coords = [point[0] for point in points]
                    y_coords = [point[1] for point in points]
                    
                    # Get bounding box
                    x_min = min(x_coords)
                    x_max = max(x_coords)
                    y_min = min(y_coords)
                    y_max = max(y_coords)
                    
                    # Convert to YOLO format (normalized center x, center y, width, height)
                    center_x = (x_min + x_max) / 2 / img_width
                    center_y = (y_min + y_max) / 2 / img_height
                    width = (x_max - x_min) / img_width
                    height = (y_max - y_min) / img_height
                    
                    # Class ID (we'll use 0 for all products since we train one model per product)
                    class_id = 0
                    
                    yolo_lines.append(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}")
        
        return yolo_lines
        
    except Exception as e:
        print(f"Error converting {json_file}: {e}")
        return []

def test_conversion():
    """Test the conversion on sample files"""
    
    # Test files
    test_files = [
        "Images/Amino_Energy_Grape-segmentation-labelme-extracted/frame_0000.json",
        "Images/Amino_Energy_Grape-segmentation-labelme-extracted/frame_0001.json",
        "Images/Amino_Energy_Grape-segmentation-labelme-extracted/frame_0002.json"
    ]
    
    for json_path in test_files:
        json_file = Path(json_path)
        png_file = json_file.with_suffix('.png')
        
        if json_file.exists() and png_file.exists():
            print(f"\n🔍 Testing: {json_file.name}")
            
            # Load image to get dimensions
            img = cv2.imread(str(png_file))
            if img is not None:
                img_height, img_width = img.shape[:2]
                print(f"📐 Image dimensions: {img_width}x{img_height}")
                
                # Convert annotation
                yolo_lines = convert_labelme_to_yolo(json_file, img_width, img_height)
                
                if yolo_lines:
                    print(f"✅ Converted successfully:")
                    for line in yolo_lines:
                        print(f"   {line}")
                else:
                    print("❌ No valid annotations found")
            else:
                print(f"❌ Could not load image: {png_file}")
        else:
            print(f"❌ Missing files: {json_file.exists()=}, {png_file.exists()=}")

if __name__ == "__main__":
    test_conversion()
