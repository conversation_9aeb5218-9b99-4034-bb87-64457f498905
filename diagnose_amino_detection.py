#!/usr/bin/env python3
"""
Diagnostic script to understand what the Amino Energy Grape model is detecting
"""

import logging
import sys
from pathlib import Path
import torch
from ultralytics import YOLO
import cv2
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def diagnose_amino_detection():
    """Diagnose what the model is detecting at different confidence levels"""
    
    # Paths
    model_path = Path("images_quadcam_results/trained_models/Amino_Energy_Grape_model/weights/best.pt")
    video_path = Path("QuadCamTestVideos/AminoEnergyGrape/cam0.mp4")
    
    # Check if model exists
    if not model_path.exists():
        logger.error(f"❌ Model not found: {model_path}")
        return False
    
    # Check if video exists
    if not video_path.exists():
        logger.error(f"❌ Video not found: {video_path}")
        return False
    
    # Load model
    logger.info(f"🤖 Loading model: {model_path}")
    model = YOLO(str(model_path))
    
    # Open video
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        logger.error(f"❌ Could not open video: {video_path}")
        return False
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    logger.info(f"📹 Video: {width}x{height}, {fps} FPS, {total_frames} frames")
    
    # Test different confidence thresholds
    confidence_levels = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    
    # Sample frames to test (every 50 frames)
    test_frames = list(range(0, total_frames, 50))
    
    logger.info(f"🔍 Testing {len(test_frames)} sample frames at {len(confidence_levels)} confidence levels")
    
    detection_stats = {}
    
    for conf_level in confidence_levels:
        detection_stats[conf_level] = {
            'total_detections': 0,
            'frames_with_detections': 0,
            'max_confidence': 0,
            'avg_confidence': 0,
            'box_sizes': []
        }
    
    for frame_idx in test_frames:
        # Seek to frame
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if not ret:
            continue
        
        logger.info(f"📋 Testing frame {frame_idx}/{total_frames}")
        
        for conf_level in confidence_levels:
            # Run YOLO with current confidence level
            results = model(frame, conf=conf_level, verbose=False)
            
            if results[0].boxes is not None and len(results[0].boxes) > 0:
                boxes = results[0].boxes
                detection_stats[conf_level]['frames_with_detections'] += 1
                
                for box in boxes:
                    conf = float(box.conf[0])
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    box_area = (x2 - x1) * (y2 - y1)
                    
                    detection_stats[conf_level]['total_detections'] += 1
                    detection_stats[conf_level]['max_confidence'] = max(detection_stats[conf_level]['max_confidence'], conf)
                    detection_stats[conf_level]['box_sizes'].append(box_area)
                    
                    logger.info(f"   📦 Conf {conf_level}: Detection with {conf:.3f} confidence, box area: {box_area:.0f}")
    
    cap.release()
    
    # Calculate averages and report
    logger.info("\n" + "="*80)
    logger.info("🔍 DETECTION ANALYSIS RESULTS")
    logger.info("="*80)
    
    for conf_level in confidence_levels:
        stats = detection_stats[conf_level]
        
        if stats['total_detections'] > 0:
            avg_conf = stats['max_confidence']  # Using max as proxy since we don't track all confidences
            avg_box_size = np.mean(stats['box_sizes']) if stats['box_sizes'] else 0
            
            logger.info(f"📊 Confidence {conf_level}:")
            logger.info(f"   - Total detections: {stats['total_detections']}")
            logger.info(f"   - Frames with detections: {stats['frames_with_detections']}/{len(test_frames)}")
            logger.info(f"   - Max confidence seen: {stats['max_confidence']:.3f}")
            logger.info(f"   - Average box size: {avg_box_size:.0f} pixels")
        else:
            logger.info(f"📊 Confidence {conf_level}: No detections")
    
    # Test a few specific frames with visualization
    logger.info("\n" + "="*80)
    logger.info("🎬 CREATING DIAGNOSTIC FRAMES")
    logger.info("="*80)
    
    diagnostic_frames = [100, 200, 300, 400]  # Test specific frames
    
    for frame_idx in diagnostic_frames:
        if frame_idx >= total_frames:
            continue
            
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if not ret:
            continue
        
        # Test with low confidence to see all detections
        results = model(frame, conf=0.1, verbose=False)
        
        output_frame = frame.copy()
        detection_count = 0
        
        if results[0].boxes is not None and len(results[0].boxes) > 0:
            boxes = results[0].boxes
            
            for i, box in enumerate(boxes):
                conf = float(box.conf[0])
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                box_area = (x2 - x1) * (y2 - y1)
                
                # Color code by confidence
                if conf >= 0.8:
                    color = (0, 255, 0)  # Green for high confidence
                elif conf >= 0.5:
                    color = (0, 255, 255)  # Yellow for medium confidence
                else:
                    color = (0, 0, 255)  # Red for low confidence
                
                # Draw bounding box
                cv2.rectangle(output_frame, (x1, y1), (x2, y2), color, 2)
                
                # Draw confidence and area
                label = f"{conf:.2f} ({box_area:.0f}px)"
                cv2.putText(output_frame, label, (x1, y1 - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                
                detection_count += 1
                logger.info(f"   Frame {frame_idx}: Detection {i+1} - Conf: {conf:.3f}, Area: {box_area:.0f}")
        
        # Save diagnostic frame
        output_path = f"diagnostic_frame_{frame_idx}.jpg"
        cv2.imwrite(output_path, output_frame)
        logger.info(f"💾 Saved diagnostic frame: {output_path} ({detection_count} detections)")
    
    logger.info("\n✅ Diagnostic analysis completed!")
    logger.info("🔍 Check the diagnostic_frame_*.jpg files to see what the model is detecting")
    
    return True

if __name__ == "__main__":
    logger.info("🔍 Diagnosing Amino Energy Grape Detection")
    logger.info("=" * 60)
    
    success = diagnose_amino_detection()
    
    if success:
        logger.info("✅ Diagnostic completed successfully!")
    else:
        logger.error("❌ Diagnostic failed!")
        sys.exit(1)
