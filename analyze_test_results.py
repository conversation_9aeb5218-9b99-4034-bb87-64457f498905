#!/usr/bin/env python3
"""
Quick analysis of the test results to see what's being detected
"""

import cv2
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_test_video():
    """Analyze the test output video to see detection quality"""
    
    video_path = Path("test_amino_single_object.mp4")
    
    if not video_path.exists():
        logger.error(f"❌ Test video not found: {video_path}")
        return False
    
    # Open video
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        logger.error(f"❌ Could not open video: {video_path}")
        return False
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    logger.info(f"📹 Analyzing video: {width}x{height}, {fps} FPS, {total_frames} frames")
    
    frame_count = 0
    detection_frames = 0
    box_sizes = []
    confidences = []
    
    # Sample every 10th frame for analysis
    sample_frames = []
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Look for green rectangles (detection boxes)
        # Convert to HSV for better green detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Define range for green color (detection boxes)
        lower_green = np.array([40, 50, 50])
        upper_green = np.array([80, 255, 255])
        
        # Create mask for green areas
        mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # Find contours (potential detection boxes)
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        has_detection = False
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 100:  # Minimum area for a detection box outline
                has_detection = True
                
                # Get bounding box of the contour
                x, y, w, h = cv2.boundingRect(contour)
                box_area = w * h
                box_sizes.append(box_area)
                
                # Try to extract confidence from text (if visible)
                # This is approximate since we're analyzing the rendered video
                break
        
        if has_detection:
            detection_frames += 1
            
            # Save sample frames for manual inspection
            if frame_count % 50 == 0:  # Every 50th frame
                sample_path = f"sample_frame_{frame_count}.jpg"
                cv2.imwrite(sample_path, frame)
                sample_frames.append(sample_path)
    
    cap.release()
    
    # Analysis results
    logger.info("\n" + "="*60)
    logger.info("📊 VIDEO ANALYSIS RESULTS")
    logger.info("="*60)
    logger.info(f"📈 Total frames: {frame_count}")
    logger.info(f"📈 Frames with detections: {detection_frames}")
    logger.info(f"📈 Detection rate: {detection_frames/frame_count*100:.1f}%")
    
    if box_sizes:
        avg_box_size = np.mean(box_sizes)
        min_box_size = np.min(box_sizes)
        max_box_size = np.max(box_sizes)
        
        logger.info(f"📦 Average box size: {avg_box_size:.0f} pixels")
        logger.info(f"📦 Box size range: {min_box_size:.0f} - {max_box_size:.0f} pixels")
        
        # Check if boxes are reasonable size
        frame_area = width * height
        avg_box_percentage = (avg_box_size / frame_area) * 100
        max_box_percentage = (max_box_size / frame_area) * 100
        
        logger.info(f"📦 Average box covers: {avg_box_percentage:.1f}% of frame")
        logger.info(f"📦 Largest box covers: {max_box_percentage:.1f}% of frame")
        
        if max_box_percentage > 25:
            logger.warning("⚠️  Some boxes are still quite large (>25% of frame)")
        elif max_box_percentage > 15:
            logger.info("ℹ️  Box sizes are reasonable but could be smaller")
        else:
            logger.info("✅ Box sizes look good (<15% of frame)")
    
    if sample_frames:
        logger.info(f"💾 Saved {len(sample_frames)} sample frames for inspection:")
        for frame_path in sample_frames:
            logger.info(f"   - {frame_path}")
    
    logger.info("\n✅ Analysis completed!")
    return True

if __name__ == "__main__":
    logger.info("🔍 Analyzing Test Results")
    logger.info("=" * 40)
    
    success = analyze_test_video()
    
    if success:
        logger.info("✅ Analysis completed successfully!")
    else:
        logger.error("❌ Analysis failed!")
