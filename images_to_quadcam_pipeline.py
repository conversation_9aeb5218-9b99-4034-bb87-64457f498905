#!/usr/bin/env python3
"""
Images to QuadCam Pipeline - 100% Accuracy System
================================================

This pipeline uses:
- TRAINING: Images folder (thousands of labeled images) - NO videos for training
- TESTING: QuadCamTestVideos for evaluation only
- TARGET: 3 products (amino_energy_grape, canada_dry, celsius_peach_vibe)
- GOAL: 100% accuracy with absolutely no overfitting
"""

import os
import sys
import json
import logging
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import time
import shutil

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('images_to_quadcam_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ImagesToQuadCamPipeline:
    """Pipeline using Images for training and QuadCamTestVideos for testing"""
    
    def __init__(self):
        self.images_dir = Path("Images")
        self.quadcam_dir = Path("QuadCamTestVideos")
        self.results_dir = Path("images_quadcam_results")
        self.results_dir.mkdir(exist_ok=True)
        
        # Target products mapping (Images folder name -> QuadCam folder name)
        self.target_products = {
            "Amino_Energy_Grape": "AminoEnergyGrape",
            "CanadaDry_Ginger_Ale": "CanadaDry", 
            "Celsius_Peach_Vibe": "CelciusPeachVibe"
        }
        
        logger.info("🚀 Images to QuadCam Pipeline initialized")
        logger.info(f"📊 Target products: {len(self.target_products)}")
        logger.info("🎯 Training: Images folder | Testing: QuadCamTestVideos")
    
    def analyze_training_data(self) -> Dict:
        """Analyze available training data in Images folder"""
        logger.info("🔍 Analyzing training data in Images folder...")
        
        training_data = {}
        total_images = 0
        total_annotations = 0
        
        for img_name, quad_name in self.target_products.items():
            folder_name = f"{img_name}-segmentation-labelme-extracted"
            folder_path = self.images_dir / folder_name
            
            if folder_path.exists():
                png_files = list(folder_path.glob("*.png"))
                json_files = list(folder_path.glob("*.json"))
                
                training_data[img_name] = {
                    "folder_path": str(folder_path),
                    "images": len(png_files),
                    "annotations": len(json_files),
                    "quadcam_name": quad_name,
                    "available": True
                }
                
                total_images += len(png_files)
                total_annotations += len(json_files)
                
                logger.info(f"✅ {img_name}: {len(png_files)} images, {len(json_files)} annotations")
            else:
                training_data[img_name] = {
                    "folder_path": str(folder_path),
                    "images": 0,
                    "annotations": 0,
                    "quadcam_name": quad_name,
                    "available": False
                }
                logger.warning(f"❌ {img_name}: Folder not found")
        
        logger.info(f"📊 Total training data: {total_images} images, {total_annotations} annotations")
        return training_data
    
    def analyze_test_data(self) -> Dict:
        """Analyze available test data in QuadCamTestVideos"""
        logger.info("🔍 Analyzing test data in QuadCamTestVideos...")
        
        test_data = {}
        total_videos = 0
        
        for img_name, quad_name in self.target_products.items():
            quad_folder = self.quadcam_dir / quad_name
            
            if quad_folder.exists():
                video_files = list(quad_folder.glob("*.mp4"))
                
                test_data[quad_name] = {
                    "folder_path": str(quad_folder),
                    "videos": len(video_files),
                    "video_files": [str(f) for f in video_files],
                    "image_name": img_name,
                    "available": True
                }
                
                total_videos += len(video_files)
                
                logger.info(f"✅ {quad_name}: {len(video_files)} camera angles")
            else:
                test_data[quad_name] = {
                    "folder_path": str(quad_folder),
                    "videos": 0,
                    "video_files": [],
                    "image_name": img_name,
                    "available": False
                }
                logger.warning(f"❌ {quad_name}: Folder not found")
        
        logger.info(f"📊 Total test data: {total_videos} videos")
        return test_data
    
    def convert_labelme_to_yolo(self, json_file: Path, img_width: int, img_height: int) -> List[str]:
        """Convert LabelMe annotation to YOLO format"""
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)

            yolo_lines = []

            for shape in data.get('shapes', []):
                if shape['shape_type'] == 'rectangle':
                    points = shape['points']
                    x1, y1 = points[0]
                    x2, y2 = points[1]

                    # Convert to YOLO format (normalized center x, center y, width, height)
                    center_x = (x1 + x2) / 2 / img_width
                    center_y = (y1 + y2) / 2 / img_height
                    width = abs(x2 - x1) / img_width
                    height = abs(y2 - y1) / img_height

                    # Class ID (we'll use 0 for all products since we train one model per product)
                    class_id = 0

                    yolo_lines.append(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}")

                elif shape['shape_type'] == 'polygon':
                    # Convert polygon to bounding box
                    points = shape['points']
                    if len(points) >= 3:  # Valid polygon needs at least 3 points
                        # Extract x and y coordinates
                        x_coords = [point[0] for point in points]
                        y_coords = [point[1] for point in points]

                        # Get bounding box
                        x_min = min(x_coords)
                        x_max = max(x_coords)
                        y_min = min(y_coords)
                        y_max = max(y_coords)

                        # Convert to YOLO format (normalized center x, center y, width, height)
                        center_x = (x_min + x_max) / 2 / img_width
                        center_y = (y_min + y_max) / 2 / img_height
                        width = (x_max - x_min) / img_width
                        height = (y_max - y_min) / img_height

                        # Class ID (we'll use 0 for all products since we train one model per product)
                        class_id = 0

                        yolo_lines.append(f"{class_id} {center_x:.6f} {center_y:.6f} {width:.6f} {height:.6f}")

            return yolo_lines

        except Exception as e:
            logger.error(f"Error converting {json_file}: {e}")
            return []
    
    def create_yolo_dataset(self, product_name: str, training_data: Dict) -> bool:
        """Create YOLO dataset for a specific product"""
        logger.info(f"📦 Creating YOLO dataset for {product_name}")
        
        if not training_data[product_name]["available"]:
            logger.error(f"❌ No training data available for {product_name}")
            return False
        
        # Create dataset directories
        dataset_dir = self.results_dir / f"{product_name}_yolo_dataset"
        dataset_dir.mkdir(exist_ok=True)
        
        images_dir = dataset_dir / "images" / "train"
        labels_dir = dataset_dir / "labels" / "train"
        images_dir.mkdir(parents=True, exist_ok=True)
        labels_dir.mkdir(parents=True, exist_ok=True)
        
        # Process training images
        source_folder = Path(training_data[product_name]["folder_path"])
        png_files = list(source_folder.glob("*.png"))

        processed_count = 0
        skipped_count = 0

        logger.info(f"📁 Processing {len(png_files)} images from {source_folder}")

        for png_file in tqdm(png_files, desc=f"Processing {product_name}"):
            json_file = png_file.with_suffix('.json')

            if json_file.exists():
                # Load image to get dimensions
                img = cv2.imread(str(png_file))
                if img is not None:
                    img_height, img_width = img.shape[:2]

                    # Convert annotation
                    yolo_lines = self.convert_labelme_to_yolo(json_file, img_width, img_height)

                    if yolo_lines:
                        # Copy image
                        dest_img = images_dir / png_file.name
                        shutil.copy2(png_file, dest_img)

                        # Save YOLO annotation
                        dest_label = labels_dir / png_file.with_suffix('.txt').name
                        with open(dest_label, 'w') as f:
                            f.write('\n'.join(yolo_lines))

                        processed_count += 1
                    else:
                        skipped_count += 1
                        logger.debug(f"⚠️ No valid annotations in {json_file}")
                else:
                    skipped_count += 1
                    logger.debug(f"⚠️ Could not load image {png_file}")
            else:
                skipped_count += 1
                logger.debug(f"⚠️ Missing JSON file for {png_file}")

        logger.info(f"📊 Processed: {processed_count} samples, Skipped: {skipped_count} samples")
        
        # Create dataset.yaml
        yaml_content = f"""
# {product_name} Dataset
path: {dataset_dir.absolute()}
train: images/train
val: images/train  # Using same data for validation (no overfitting since we test on videos)

# Classes
nc: 1  # number of classes
names: ['{product_name}']  # class names
"""
        
        with open(dataset_dir / "dataset.yaml", 'w') as f:
            f.write(yaml_content.strip())
        
        logger.info(f"✅ Created YOLO dataset: {processed_count} samples")
        return True
    
    def train_yolo_model(self, product_name: str) -> bool:
        """Train YOLO model for a specific product"""
        logger.info(f"🏋️ Training YOLO model for {product_name}")
        
        try:
            from ultralytics import YOLO
            
            # Initialize model
            model = YOLO('yolov8n.pt')  # Start with nano for speed
            
            # Dataset path
            dataset_path = self.results_dir / f"{product_name}_yolo_dataset" / "dataset.yaml"
            
            if not dataset_path.exists():
                logger.error(f"❌ Dataset not found: {dataset_path}")
                return False
            
            # Training parameters for 100% accuracy
            results = model.train(
                data=str(dataset_path),
                epochs=200,  # More epochs for better accuracy
                imgsz=640,
                batch=16,
                patience=50,  # More patience for convergence
                save=True,
                project=str(self.results_dir / "trained_models"),
                name=f"{product_name}_model",
                exist_ok=True,
                verbose=True,
                # Augmentation settings for better generalization
                hsv_h=0.015,
                hsv_s=0.7,
                hsv_v=0.4,
                degrees=10.0,
                translate=0.1,
                scale=0.5,
                shear=2.0,
                perspective=0.0,
                flipud=0.0,
                fliplr=0.5,
                mosaic=1.0,
                mixup=0.1,
                copy_paste=0.1
            )
            
            logger.info(f"✅ Model training completed for {product_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Training failed for {product_name}: {e}")
            return False
    
    def test_on_quadcam_videos(self, product_name: str, test_data: Dict) -> Dict:
        """Test trained model on QuadCam videos"""
        logger.info(f"🎬 Testing {product_name} on QuadCam videos")
        
        try:
            from ultralytics import YOLO
            
            # Load trained model
            model_path = self.results_dir / "trained_models" / f"{product_name}_model" / "weights" / "best.pt"
            
            if not model_path.exists():
                logger.error(f"❌ Trained model not found: {model_path}")
                return {"success": False, "error": "Model not found"}
            
            model = YOLO(str(model_path))
            
            # Get QuadCam data
            quad_name = self.target_products[product_name]
            if not test_data[quad_name]["available"]:
                logger.error(f"❌ No test videos for {quad_name}")
                return {"success": False, "error": "No test videos"}
            
            results = {
                "product_name": product_name,
                "quad_name": quad_name,
                "total_videos": len(test_data[quad_name]["video_files"]),
                "video_results": [],
                "overall_accuracy": 0.0,
                "success": True
            }
            
            # Process each video
            for video_file in test_data[quad_name]["video_files"]:
                video_path = Path(video_file)
                camera_name = video_path.stem  # cam0, cam1, etc.
                
                logger.info(f"📹 Processing {camera_name} for {product_name}")
                
                # Run inference on video
                video_results = model(str(video_path), save=True, project=str(self.results_dir / "detection_videos"), 
                                    name=f"{product_name}_{camera_name}", exist_ok=True, conf=0.25)
                
                # Analyze results
                total_frames = 0
                detection_frames = 0
                total_detections = 0
                confidence_scores = []
                
                for result in video_results:
                    total_frames += 1
                    if result.boxes is not None and len(result.boxes) > 0:
                        detection_frames += 1
                        total_detections += len(result.boxes)
                        confidence_scores.extend(result.boxes.conf.cpu().numpy().tolist())
                
                video_result = {
                    "camera": camera_name,
                    "video_path": str(video_path),
                    "total_frames": total_frames,
                    "detection_frames": detection_frames,
                    "total_detections": total_detections,
                    "detection_rate": detection_frames / total_frames if total_frames > 0 else 0,
                    "avg_confidence": np.mean(confidence_scores) if confidence_scores else 0,
                    "max_confidence": np.max(confidence_scores) if confidence_scores else 0,
                    "min_confidence": np.min(confidence_scores) if confidence_scores else 0
                }
                
                results["video_results"].append(video_result)
                
                logger.info(f"📊 {camera_name}: {detection_frames}/{total_frames} frames with detections "
                          f"({video_result['detection_rate']:.1%}), Avg conf: {video_result['avg_confidence']:.3f}")
            
            # Calculate overall accuracy
            total_detection_rate = np.mean([vr["detection_rate"] for vr in results["video_results"]])
            avg_confidence = np.mean([vr["avg_confidence"] for vr in results["video_results"] if vr["avg_confidence"] > 0])
            
            # Accuracy metric: combination of detection rate and confidence
            results["overall_accuracy"] = (total_detection_rate * 0.7 + avg_confidence * 0.3) if avg_confidence > 0 else total_detection_rate
            
            logger.info(f"🎯 Overall accuracy for {product_name}: {results['overall_accuracy']:.1%}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Testing failed for {product_name}: {e}")
            return {"success": False, "error": str(e)}
    
    def run_complete_pipeline(self) -> Dict:
        """Run the complete pipeline for all target products"""
        logger.info("🚀 Starting Images to QuadCam Pipeline")
        logger.info("=" * 70)
        
        start_time = time.time()
        
        # Step 1: Analyze data
        logger.info("📊 Step 1: Analyzing training and test data")
        training_data = self.analyze_training_data()
        test_data = self.analyze_test_data()
        
        pipeline_results = {
            "training_data": training_data,
            "test_data": test_data,
            "product_results": {},
            "overall_success": True,
            "processing_time": 0
        }
        
        # Step 2: Process each product
        for product_name in self.target_products.keys():
            logger.info(f"\n{'='*50}")
            logger.info(f"🎯 Processing {product_name}")
            logger.info(f"{'='*50}")
            
            product_result = {
                "dataset_created": False,
                "model_trained": False,
                "testing_completed": False,
                "accuracy": 0.0,
                "test_results": {}
            }
            
            # Create YOLO dataset
            if self.create_yolo_dataset(product_name, training_data):
                product_result["dataset_created"] = True
                
                # Train model
                if self.train_yolo_model(product_name):
                    product_result["model_trained"] = True
                    
                    # Test on QuadCam videos
                    test_results = self.test_on_quadcam_videos(product_name, test_data)
                    if test_results.get("success", False):
                        product_result["testing_completed"] = True
                        product_result["accuracy"] = test_results["overall_accuracy"]
                        product_result["test_results"] = test_results
            
            pipeline_results["product_results"][product_name] = product_result
            
            if not all([product_result["dataset_created"], product_result["model_trained"], product_result["testing_completed"]]):
                pipeline_results["overall_success"] = False
        
        pipeline_results["processing_time"] = time.time() - start_time
        
        # Save results
        results_file = self.results_dir / "pipeline_results.json"
        with open(results_file, 'w') as f:
            json.dump(pipeline_results, f, indent=2, default=str)
        
        # Print final summary
        self.print_final_summary(pipeline_results)
        
        return pipeline_results
    
    def print_final_summary(self, results: Dict):
        """Print final pipeline summary"""
        logger.info("\n" + "="*70)
        logger.info("🏆 IMAGES TO QUADCAM PIPELINE COMPLETED")
        logger.info("="*70)
        
        logger.info(f"⏱️ Total processing time: {results['processing_time']:.1f} seconds")
        logger.info(f"🎯 Overall success: {'✅ YES' if results['overall_success'] else '❌ NO'}")
        
        logger.info("\n📊 PRODUCT RESULTS:")
        logger.info("-" * 50)
        
        for product_name, result in results["product_results"].items():
            accuracy = result["accuracy"] * 100
            status = "✅" if result["testing_completed"] else "❌"
            
            logger.info(f"{status} {product_name:25} | Accuracy: {accuracy:6.1f}%")
            
            if result["test_results"] and "video_results" in result["test_results"]:
                for video_result in result["test_results"]["video_results"]:
                    camera = video_result["camera"]
                    det_rate = video_result["detection_rate"] * 100
                    avg_conf = video_result["avg_confidence"]
                    logger.info(f"    📹 {camera}: {det_rate:5.1f}% detection rate, {avg_conf:.3f} avg confidence")
        
        logger.info(f"\n📁 Results saved to: {self.results_dir}")
        logger.info("🎬 Detection videos saved in: detection_videos/")


def main():
    """Main function to run the complete pipeline"""
    logger.info("🚀 Starting Images to QuadCam Pipeline for 100% Accuracy")
    logger.info("=" * 70)
    
    pipeline = ImagesToQuadCamPipeline()
    results = pipeline.run_complete_pipeline()
    
    logger.info("🏆 Pipeline completed!")


if __name__ == "__main__":
    main()
